package com.czb.hn.service.business.impl.briefing;

import com.czb.hn.dto.PlanDTO;
import com.czb.hn.dto.briefing.BriefingConfigurationCreateDto;
import com.czb.hn.dto.briefing.BriefingConfigurationResponseDto;
import com.czb.hn.dto.briefing.BriefingConfigurationUpdateDto;
import com.czb.hn.dto.briefing.config.BriefingReceptionSettingsDto;
import com.czb.hn.dto.briefing.config.ContentSettingsDto;
import com.czb.hn.jpa.securadar.entity.BriefingConfiguration;
import com.czb.hn.jpa.securadar.repository.BriefingConfigurationRepository;
import com.czb.hn.service.bulletin.BulletinJobService;
import com.czb.hn.service.business.PlanService;
import com.czb.hn.util.BriefingConfigurationMapper;
import org.slf4j.Logger;
import com.czb.hn.service.business.BriefingConfigurationService;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Service;



@Service
@Transactional
public class BriefingConfigurationServiceImpl implements BriefingConfigurationService {

    private static final Logger logger = LoggerFactory.getLogger(BriefingConfigurationServiceImpl.class);

    @Autowired
    private BriefingConfigurationRepository briefingConfigurationRepository;

    @Autowired
    private BriefingConfigurationMapper briefingConfigurationMapper;

    @Autowired
    private BulletinJobService bulletinJobService;

    @Autowired
    private PlanService planService;

    @Override
    public BriefingConfigurationResponseDto getConfigurationsByPlanId(Long planId) {
        try {
            logger.info("Retrieving briefing configuration by plan ID: {}", planId);

            BriefingConfiguration configuration = briefingConfigurationRepository.findByPlanId(planId);

            if (configuration == null) {
                logger.warn("Briefing configuration not found for plan ID: {}. Create default configuration.", planId);
                PlanDTO plan = planService.getPlanById(planId);
                BriefingConfigurationCreateDto createDto = new BriefingConfigurationCreateDto(
                        plan.name(),
                        planId,
                        false,
                        new ContentSettingsDto(null, null,  null, 0, null,  null, null),
                        false,
                        new BriefingReceptionSettingsDto(
                                "10:00",
                                new BriefingReceptionSettingsDto.ReceptionMethodsDto(new BriefingReceptionSettingsDto.EmailConfigDto(false, null), new BriefingReceptionSettingsDto.SmsConfigDto(false, null))
                        ),
                        false,
                        new BriefingReceptionSettingsDto(
                                "10:00",
                                new BriefingReceptionSettingsDto.ReceptionMethodsDto(new BriefingReceptionSettingsDto.EmailConfigDto(false, null), new BriefingReceptionSettingsDto.SmsConfigDto(false, null))
                        ),
                        false,
                        new BriefingReceptionSettingsDto(
                                "10:00",
                                new BriefingReceptionSettingsDto.ReceptionMethodsDto(new BriefingReceptionSettingsDto.EmailConfigDto(false, null), new BriefingReceptionSettingsDto.SmsConfigDto(false, null))
                        )
                );

                BriefingConfiguration createConfiguration = briefingConfigurationMapper.toEntity(createDto);

                BriefingConfiguration savedConfiguration = briefingConfigurationRepository.save(createConfiguration);

                logger.info("Successfully created briefing configuration with ID: {}", savedConfiguration.getId());

                //传递创建的planId
                //bulletinJobService.createBulletinConfiguration(savedConfiguration.getPlanId());
                return briefingConfigurationMapper.toResponseDto(savedConfiguration);
            }

            logger.info("Successfully retrieved briefing configuration for plan ID: {}", planId);
            return briefingConfigurationMapper.toResponseDto(configuration);
        } catch (Exception e) {
            logger.error("Error retrieving briefing configuration by plan ID {}: {}", planId, e.getMessage(), e);
            throw new RuntimeException("Failed to retrieve briefing configuration: " + e.getMessage(), e);
        }
    }

    @Override
    public BriefingConfigurationResponseDto updateConfiguration(Long id, BriefingConfigurationUpdateDto updateDto){
        try {
            logger.info("Updating briefing configuration with ID: {}", id);

            BriefingConfiguration existingConfiguration = briefingConfigurationRepository.findById(id)
                    .orElseThrow(() -> new IllegalArgumentException("Briefing configuration not found with ID: " + id));

            // Update the configuration
            briefingConfigurationMapper.updateEntityFromDto(updateDto, existingConfiguration);

            BriefingConfiguration savedConfiguration = briefingConfigurationRepository.save(existingConfiguration);

            logger.info("Successfully updated alert configuration with ID: {}", id);

            bulletinJobService.createBulletinConfiguration(savedConfiguration.getPlanId());
            return briefingConfigurationMapper.toResponseDto(savedConfiguration);

        } catch (Exception e) {
            logger.error("Error updating alert configuration with ID {}: {}", id, e.getMessage(), e);
            throw new RuntimeException("Failed to update alert configuration: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Boolean isConfigurationPlanIdAvailable(Long planId) {
        return !briefingConfigurationRepository.existsByPlanId(planId);
    }
}
