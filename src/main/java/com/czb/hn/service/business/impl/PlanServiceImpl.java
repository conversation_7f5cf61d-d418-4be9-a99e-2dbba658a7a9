package com.czb.hn.service.business.impl;

import cn.com.ycfin.onepass.client.model.Group;
import cn.com.ycfin.onepass.client.model.UserRole;

import com.czb.hn.constant.CommonConstants;
import com.czb.hn.dto.EnterpriseDTO;
import com.czb.hn.dto.PlanCreateDTO;
import com.czb.hn.dto.PlanDTO;
import com.czb.hn.dto.PlanUpdateDTO;
import com.czb.hn.dto.user.LoginUser;
import com.czb.hn.dto.user.LoginUserContextHolder;
import com.czb.hn.jpa.securadar.entity.Plan;
import com.czb.hn.jpa.securadar.repository.PlanRepository;
import com.czb.hn.service.business.GroupCacheService;
import com.czb.hn.service.business.PlanService;
import com.czb.hn.util.KeywordUtils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class PlanServiceImpl implements PlanService {

    @Autowired
    private PlanRepository planRepository;

    @Autowired
    private GroupCacheService groupCacheService;

    @Override
    @Transactional
    public PlanDTO createPlan(PlanCreateDTO planCreateDTO) {
        // Find enterprises by their IDs
        // Create new plan entity
        Plan plan = new Plan();
        plan.setName(planCreateDTO.name());
        plan.setDescription(planCreateDTO.description());
        plan.setMonitorKeywords(planCreateDTO.monitorKeywords().trim());

        String excludeKeywords = planCreateDTO.excludeKeywords();
        if (excludeKeywords != null && !excludeKeywords.trim().isEmpty()) {
            plan.setExcludeKeywords(excludeKeywords.trim());
        } else {
            plan.setExcludeKeywords(null);
        }

        plan.setEnterpriseId(planCreateDTO.enterpriseId());

        // Save the plan
        Plan savedPlan = planRepository.save(plan);

        // Convert to DTO and return
        return convertToDTO(savedPlan);
    }

    @Override
    public PlanDTO getPlanById(Long id) {
        Plan plan = planRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Plan not found with ID: " + id));
        return convertToDTO(plan);
    }

    @Override
    @Transactional
    public PlanDTO updatePlan(Long id, PlanUpdateDTO planUpdateDTO) {
        Plan plan = planRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Plan not found with ID: " + id));

        // Update fields if they are provided
        if (planUpdateDTO.name() != null) {
            plan.setName(planUpdateDTO.name());
        }

        if (planUpdateDTO.description() != null) {
            plan.setDescription(planUpdateDTO.description());
        }

        if (planUpdateDTO.monitorKeywords() != null) {
            plan.setMonitorKeywords(planUpdateDTO.monitorKeywords().trim());
        }

        if (planUpdateDTO.excludeKeywords() != null) {
            if (planUpdateDTO.excludeKeywords().trim().isEmpty()) {
                plan.setExcludeKeywords(null);
            } else {
                plan.setExcludeKeywords(planUpdateDTO.excludeKeywords().trim());
            }
        }

        if (planUpdateDTO.enterpriseId() != null) {
            plan.setEnterpriseId(planUpdateDTO.enterpriseId());
        }

        // Save updated plan
        Plan updatedPlan = planRepository.save(plan);
        return convertToDTO(updatedPlan);
    }

    @Override
    @Transactional
    public void deletePlan(Long id) {
        if (!planRepository.existsById(id)) {
            throw new IllegalArgumentException("Plan not found with ID: " + id);
        }
        planRepository.deleteById(id);
    }

    @Override
    public List<PlanDTO> getAllPlans() {
        return planRepository.findAll().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<PlanDTO> getPlansByEnterpriseId(String enterpriseId) {
        return planRepository.findByVisibleEnterpriseId(enterpriseId).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * Convert a Plan entity to a PlanDTO
     *
     * @param plan the plan entity
     * @return the plan DTO
     */
    private PlanDTO convertToDTO(Plan plan) {

        // todo 后期会补充企业名称
        String enterpriseName = "企业名称";

        return new PlanDTO(
                plan.getId(),
                plan.getName(),
                plan.getDescription(),
                plan.getMonitorKeywords(),
                plan.getExcludeKeywords(),
                new EnterpriseDTO(plan.getEnterpriseId(), enterpriseName),
                plan.getCreatedAt(),
                plan.getUpdatedAt());
    }

    @Override
    public KeywordUtils.KeywordParseResult getPlanKeywordsWithLogic(Long planId) {
        Plan plan = planRepository.findById(planId)
                .orElseThrow(() -> new IllegalArgumentException("Plan not found with ID: " + planId));

        return KeywordUtils.parseKeywordsWithLogic(plan.getMonitorKeywords(), plan.getExcludeKeywords());
    }

    @Override
    public List<EnterpriseDTO> getVisibleEnterprise() {
        LoginUser user = LoginUserContextHolder.getUser();
        if (user == null) {
            return List.of();
        }
        List<UserRole> userRoles = user.getUserRoles();
        boolean systemAdmin = userRoles.stream().anyMatch(userRole -> userRole.getRoleName().equals(CommonConstants.SYSTEM_ADMIN));

        boolean enterpriseAdmin = userRoles.stream().anyMatch(userRole -> userRole.getRoleName().equals(CommonConstants.ENTERPRISE_ADMIN));


        if (systemAdmin) {
            return groupCacheService.getAllGroups().stream().filter(group -> StringUtils.isNotBlank(group.groupCode()))
                    .map(group -> new EnterpriseDTO(group.groupId(), group.groupName()))
                    .toList();
        }

        if (enterpriseAdmin) {
            return List.of(new EnterpriseDTO(user.getPrimaryGroupId(), user.getPrimaryGroupName()));
        }

        return List.of();
    }
}