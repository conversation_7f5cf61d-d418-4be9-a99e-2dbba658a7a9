package com.czb.hn.service.business.impl.esrealtimeservice;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.TextQueryType;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;

import co.elastic.clients.json.JsonData;

import co.elastic.clients.util.NamedValue;
import com.czb.hn.dto.response.briefing.*;
import com.czb.hn.dto.response.search.SinaNewsSearchResponseDto;
import com.czb.hn.service.business.PlanService;
import com.czb.hn.util.KeywordUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.czb.hn.service.business.ElasticsearchBriefingService;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 新浪舆情Elasticsearch聚合查询服务实现
 * 使用ElasticsearchClient实现
 */
@Service
public class ElasticsearchBriefingServiceImpl implements ElasticsearchBriefingService {
    
    private static final Logger logger = LoggerFactory.getLogger(ElasticsearchBriefingServiceImpl.class);
    
    @Autowired
    private ElasticsearchClient elasticsearchClient;

    @Autowired
    private PlanService planService;

    private static final String INDEX_NAME = "sina_news";

    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public HistogramSummeryDto getSensitiveInfoTrend(
            String startTime,
            String endTime,
            Long planId,
            List<Integer> sensitivityType,
            List<String> mediaTypes,
            List<Integer> contentTypes,
            Integer isOriginal,
            List<String> SecondTrades,
            List<String> MediaLevel) {
        // 参数校验（符合Spring框架开发规范）
        if (startTime == null || endTime == null) {
            throw new IllegalArgumentException("Start and end times must not be null");
        }
        // 获取方案的关键词信息
        KeywordUtils.KeywordParseResult planKeywordsWithLogic = planService.getPlanKeywordsWithLogic(planId);
        KeywordUtils.MonitorKeywordGroup monitorKeywordsWithLogic = planKeywordsWithLogic.getMonitorKeywords();
        List<String> includeKeywords = monitorKeywordsWithLogic.getOrKeywords();
        List<List<String>> keywordAndGroups = monitorKeywordsWithLogic.getAndGroups();
        for (List<String> andGroup : keywordAndGroups) {
            includeKeywords.addAll(andGroup);
        }

        //初始化全局变量
        HistogramSummeryDto histogramSummeryDto = new HistogramSummeryDto();
        Double totalCount = 0.0;

        //获取总条数
        try {
            BoolQuery.Builder boolQueryBuilder = buildAggregationQuery(
                    includeKeywords,
                    mediaTypes,
                    sensitivityType,
                    startTime,
                    endTime,
                    contentTypes,
                    isOriginal,
                    SecondTrades,
                    MediaLevel
            );
            SearchRequest request = SearchRequest.of(r -> r
                    .index(INDEX_NAME)
                    .size(0)
                    .query(boolQueryBuilder.build()._toQuery())
                    .aggregations("total_count", a -> a
                            .valueCount(v -> v
                                    .field("mediaTypes")))
            );
            SearchResponse<Void> response = elasticsearchClient.search(request, Void.class);
            totalCount = response.aggregations().get("total_count").valueCount().value();
        } catch (Exception e) {
            logger.error("Error in get total count: {}", e.getMessage(), e);
        }

        //逐一获取敏感信息分布
        for (Integer sensitivity : sensitivityType) {

            List<HistogramDto> result = new ArrayList<>();
            SensitivityTypeDistributionDto sensitivityTypeDistribution = new SensitivityTypeDistributionDto();

            try {
                List<Integer> list = new ArrayList<>();
                list.add(sensitivity);
                BoolQuery.Builder boolQueryBuilder = buildAggregationQuery(
                        includeKeywords,
                        mediaTypes,
                        list,
                        startTime,
                        endTime,
                        contentTypes,
                        isOriginal,
                        SecondTrades,
                        MediaLevel
                );

                SearchRequest request = SearchRequest.of(r -> r
                        .index(INDEX_NAME)
                        .size(0)
                        .query(boolQueryBuilder.build()._toQuery())
                        .aggregations("time_histogram", a -> a
                                .dateHistogram(dh -> dh
                                        .field("publishTime")
                                        .fixedInterval(fi -> fi.time("1h"))
                                        .format("yyyy-MM-dd HH:mm:ss")
                                )
                        )
                        .aggregations("sensitivityType_agg", a -> a
                                .terms(t -> t
                                        .field("sensitivityTypes")
                                )
                        )
                );

                SearchResponse<Void> response = elasticsearchClient.search(request, Void.class);

                //提取时间直方图
                var buckets = response.aggregations()
                        .get("time_histogram")
                        .dateHistogram()
                        .buckets()
                        .array();

                for (var bucket : buckets) {
                    LocalDateTime time = LocalDateTime.parse(bucket.keyAsString(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    result.add(new HistogramDto(time, bucket.docCount()));
                }

                logger.info("Successfully executed sensitive info trend aggregation query. Total buckets: {}", buckets.size());

                //提取敏感信息分布
                var buckets2 = response.aggregations()
                        .get("sensitivityType_agg")
                        .lterms()
                        .buckets()
                        .array();
                for (var bucket : buckets2) {
                    sensitivityTypeDistribution.setCount(bucket.docCount());
                    sensitivityTypeDistribution.setPercentage(new BigDecimal((double) bucket.docCount() / totalCount)
                            .setScale(2, RoundingMode.HALF_UP).doubleValue());
                }

                //设置敏感信息分布和敏感信息直方图
                if (sensitivity == 1) {
                    histogramSummeryDto.setSensitiveInfoTrend(result);
                    histogramSummeryDto.setSensitiveInfoDistribution(sensitivityTypeDistribution);
                } else if (sensitivity == 2) {
                    histogramSummeryDto.setNonSensitiveInfoTrend(result);
                    histogramSummeryDto.setNonSensitiveInfoDistribution(sensitivityTypeDistribution);
                } else if (sensitivity == 3) {
                    histogramSummeryDto.setNeutralInfoTrend(result);
                    histogramSummeryDto.setNeutralInfoDistribution(sensitivityTypeDistribution);
                } else {
                    throw new IllegalArgumentException("Sensitivity type must be 1,2,3");
                }

            } catch (Exception e) {
                logger.error("Error in getSensitiveInfoTrend: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to execute sensitive info trend aggregation", e);
            }
        }
        return histogramSummeryDto;
    }

    public List<MediaDistributionDto> MediaDistribution(
            String startTime,
            String endTime,
            Long planId,
            List<Integer> sensitivityType,
            List<String> mediaTypes,
            List<Integer> contentTypes,
            Integer isOriginal,
            List<String> SecondTrades,
            List<String> MediaLevel) {
        if (startTime == null || endTime == null) {
            throw new IllegalArgumentException("Start and end times must not be null");
        }

        // 获取方案的关键词信息
        KeywordUtils.KeywordParseResult planKeywordsWithLogic = planService.getPlanKeywordsWithLogic(planId);
        KeywordUtils.MonitorKeywordGroup monitorKeywordsWithLogic = planKeywordsWithLogic.getMonitorKeywords();
        List<String> includeKeywords = monitorKeywordsWithLogic.getOrKeywords();
        List<List<String>> keywordAndGroups = monitorKeywordsWithLogic.getAndGroups();
        for (List<String> andGroup : keywordAndGroups) {
            includeKeywords.addAll(andGroup);
        }

        List<MediaDistributionDto> MediaDistribution = new ArrayList<>();
        try {
            BoolQuery.Builder boolQueryBuilder = buildAggregationQuery(
                    includeKeywords,
                    mediaTypes,
                    sensitivityType,
                    startTime,
                    endTime,
                    contentTypes,
                    isOriginal,
                    SecondTrades,
                    MediaLevel
            );
            // 构建最终的搜索请求
            SearchRequest searchRequest = SearchRequest.of(builder -> {
                builder.index(INDEX_NAME)
                        .size(0)
                        .query(boolQueryBuilder.build()._toQuery())
                        .aggregations("total_count", a -> a
                                .valueCount(v -> v
                                        .field("mediaTypes") // 使用合适的字段名，如 "id" 或其他存在的字段
                                )
                        )
                        .aggregations("mediaType_agg", a -> a
                                .terms(t -> t
                                        .field("mediaTypes")
                                        .size(20)
                                )
                        );
                return builder;
            });
            //执行搜索
            SearchResponse<Void> response = elasticsearchClient.search(searchRequest, Void.class);

            var buckets1 = response.aggregations()
                    .get("mediaType_agg")
                    .sterms()
                    .buckets()
                    .array();

            MediaDistribution.add(new MediaDistributionDto("全部", (long)response.aggregations().get("total_count").valueCount().value(), null));
            Double totalCount = response.aggregations().get("total_count").valueCount().value();
            for (var bucket : buckets1) {
                MediaDistribution.add(new MediaDistributionDto(bucket.key().stringValue(), bucket.docCount(), (double) Math.round((bucket.docCount() / totalCount * 100) * 100) / 100));
            }
            return MediaDistribution;
        } catch (Exception e) {
            logger.error("Error in MediaDistribution: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to execute media distribution aggregation", e);
        }
    }

    public List<MediaTierDto> MediaDetail(
            String startTime,
            String endTime,
            Long planId,
            List<Integer> sensitivityType,
            List<String> mediaTypes,
            List<Integer> contentTypes,
            Integer isOriginal,
            List<String> SecondTrades,
            List<String> MediaLevel) {
        if (startTime == null || endTime == null) {
            throw new IllegalArgumentException("Start and end times must not be null");
        }

        // 获取方案的关键词信息
        KeywordUtils.KeywordParseResult planKeywordsWithLogic = planService.getPlanKeywordsWithLogic(planId);
        KeywordUtils.MonitorKeywordGroup monitorKeywordsWithLogic = planKeywordsWithLogic.getMonitorKeywords();
        List<String> includeKeywords = monitorKeywordsWithLogic.getOrKeywords();
        List<List<String>> keywordAndGroups = monitorKeywordsWithLogic.getAndGroups();
        for (List<String> andGroup : keywordAndGroups) {
            includeKeywords.addAll(andGroup);
        }

        List<MediaTierDto> MediaTier = new ArrayList<>();
        try {
            BoolQuery.Builder boolQueryBuilder = buildAggregationQuery(
                    includeKeywords,
                    mediaTypes,
                    sensitivityType,
                    startTime,
                    endTime,
                    contentTypes,
                    isOriginal,
                    SecondTrades,
                    MediaLevel
            );
            // 构建最终的搜索请求
            SearchRequest searchRequest = SearchRequest.of(builder -> {
                builder.index(INDEX_NAME)
                        .size(0)
                        .query(boolQueryBuilder.build()._toQuery())
                        .aggregations("total_count", a -> a
                                .valueCount(v -> v
                                        .field("mediaTypes") // 使用合适的字段名，如 "id" 或其他存在的字段
                                )
                        )
                        .aggregations("mediaTier_agg", a -> a
                                .terms(t -> t
                                        .field("mediaLevels")
                                        .size(20)
                                )
                        );
                return builder;
            });
            //执行搜索
            SearchResponse<Void> response = elasticsearchClient.search(searchRequest, Void.class);

            var buckets1 = response.aggregations()
                    .get("mediaTier_agg")
                    .sterms()
                    .buckets()
                    .array();

            MediaTier.add(new MediaTierDto("全部", (long)response.aggregations().get("total_count").valueCount().value(), null));
            Double totalCount = response.aggregations().get("total_count").valueCount().value();
            for (var bucket : buckets1) {
                MediaTier.add(new MediaTierDto(bucket.key().stringValue(), bucket.docCount(), (double) Math.round((bucket.docCount() / totalCount * 100) * 100) / 100));
            }
            return MediaTier;
        } catch (Exception e) {
            logger.error("Error in MediaDistribution: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to execute media distribution aggregation", e);
        }
    }

    public List<HighFrequencyWordDto> HighFrequencyWords(
            String startTime,
            String endTime,
            Long planId,
            List<Integer> sensitivityType,
            List<String> mediaTypes,
            List<Integer> contentTypes,
            Integer isOriginal,
            List<String> SecondTrades,
            List<String> MediaLevel) {
        if (startTime == null || endTime == null) {
            throw new IllegalArgumentException("Start and end times must not be null");
        }

        // 获取方案的关键词信息
        KeywordUtils.KeywordParseResult planKeywordsWithLogic = planService.getPlanKeywordsWithLogic(planId);
        KeywordUtils.MonitorKeywordGroup monitorKeywordsWithLogic = planKeywordsWithLogic.getMonitorKeywords();
        List<String> includeKeywords = monitorKeywordsWithLogic.getOrKeywords();
        List<List<String>> keywordAndGroups = monitorKeywordsWithLogic.getAndGroups();
        for (List<String> andGroup : keywordAndGroups) {
            includeKeywords.addAll(andGroup);
        }

        List<HighFrequencyWordDto> highFrequencyWords = new ArrayList<>();

        try {

            BoolQuery.Builder boolQueryBuilder = buildAggregationQuery(
                    includeKeywords,
                    mediaTypes,
                    sensitivityType,
                    startTime,
                    endTime,
                    contentTypes,
                    isOriginal,
                    SecondTrades,
                    MediaLevel
            );

            // 构建最终的搜索请求
            SearchRequest searchRequest = SearchRequest.of(builder -> {
                builder.index(INDEX_NAME)
                        .size(0)
                        .query(boolQueryBuilder.build()._toQuery())
                        .aggregations("highFrequencyWords_agg", a -> a
                                .terms(t -> t
                                        .field("summaryClean")
                                        .size(20)
                                        .order(NamedValue.of("_count", SortOrder.Desc)
                                        )
                                )
                        );
                return builder;
            });

            //执行搜索
            SearchResponse<Void> response = elasticsearchClient.search(searchRequest, Void.class);

            var buckets4 = response.aggregations()
                    .get("highFrequencyWords_agg")
                    .sterms()
                    .buckets()
                    .array();
            for (var bucket : buckets4){
                highFrequencyWords.add(new HighFrequencyWordDto(bucket.key().stringValue(), bucket.docCount()));
            }

            return highFrequencyWords;

        }
        catch (Exception e) {
            logger.error("Error in HighFrequencyWords: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to execute high frequency word aggregation", e);
        }
    }

    public List<EmotionDistributionDto> EmotionDistribution(
            String startTime,
            String endTime,
            Long planId,
            List<Integer> sensitivityType,
            List<String> mediaTypes,
            List<Integer> contentTypes,
            Integer isOriginal,
            List<String> SecondTrades,
            List<String> MediaLevel) {
        if (startTime == null || endTime == null) {
            throw new IllegalArgumentException("Start and end times must not be null");
        }

        // 获取方案的关键词信息
        KeywordUtils.KeywordParseResult planKeywordsWithLogic = planService.getPlanKeywordsWithLogic(planId);
        KeywordUtils.MonitorKeywordGroup monitorKeywordsWithLogic = planKeywordsWithLogic.getMonitorKeywords();
        List<String> includeKeywords = monitorKeywordsWithLogic.getOrKeywords();
        List<List<String>> keywordAndGroups = monitorKeywordsWithLogic.getAndGroups();
        for (List<String> andGroup : keywordAndGroups) {
            includeKeywords.addAll(andGroup);
        }

        List<EmotionDistributionDto> emotionDistribution = new ArrayList<>();

        try {

            BoolQuery.Builder boolQueryBuilder = buildAggregationQuery(
                    includeKeywords,
                    mediaTypes,
                    sensitivityType,
                    startTime,
                    endTime,
                    contentTypes,
                    isOriginal,
                    SecondTrades,
                    MediaLevel
            );

            // 构建最终的搜索请求
            SearchRequest searchRequest = SearchRequest.of(builder -> {
                builder.index(INDEX_NAME)
                        .size(0)
                        .query(boolQueryBuilder.build()._toQuery())
                        .aggregations("emotion_agg", a -> a
                                .terms(t -> t
                                        .field("emotion")
                                )
                        );
                return builder;
            });

            //执行搜索
            SearchResponse<Void> response = elasticsearchClient.search(searchRequest, Void.class);

            var buckets3 = response.aggregations()
                    .get("emotion_agg")
                    .sterms()
                    .buckets()
                    .array();
            emotionDistribution.add(new EmotionDistributionDto("全部", (long)response.aggregations().get("total_count").valueCount().value()));
            for (var bucket : buckets3) {
                emotionDistribution.add(new EmotionDistributionDto(bucket.key().stringValue(), bucket.docCount()));
            }

            return emotionDistribution;

        }
        catch (Exception e) {
            logger.error("Error in EmotionDistribution: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to execute emotion distribution aggregation", e);
        }
    }

    /**
     * 构建带聚合的Elasticsearch查询
     * @param keywordOrList 需要匹配的关键词列表
     * @param mediaTypes 媒体类型过滤条件
     * @param sensitivityType 敏感度等级
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param contentTypes 内容类型过滤条件
     * @return SearchSourceBuilder 聚合查询构建器
     */
    private BoolQuery.Builder buildAggregationQuery(
            List<String> keywordOrList,
            List<String> mediaTypes,
            List<Integer> sensitivityType,
            String startTime,
            String endTime,
            List<Integer> contentTypes,
            Integer isOriginal,
            List<String> secondTrades,
            List<String> mediaLevel) {

        // 构建布尔查询
        BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();

        // 添加OR匹配条件（至少匹配一个关键词）
        if (keywordOrList != null && !keywordOrList.isEmpty()) {
            for (String keyword : keywordOrList) {
                if (!keyword.isEmpty()) {
                    boolQueryBuilder.should(mq -> mq
                            .multiMatch(mm -> mm
                                    .fields("title", "content", "summary", "keywords")
                                    .query(keyword)
                                    .type(TextQueryType.Phrase)
                            )
                    );
                }
            }
            //至少匹配一个关键词
            boolQueryBuilder.minimumShouldMatch("1");
        }


        // 媒体类型过滤
        if (mediaTypes != null && !mediaTypes.isEmpty()) {
            boolQueryBuilder.filter(fq -> fq
                    .terms(t -> t
                            .field("mediaTypes")
                            .terms(b -> b
                                    .value(mediaTypes.stream()
                                            .map(FieldValue::of)
                                            .collect(Collectors.toList())
                                    )
                            )
                    )
            );
        }

        // 内容类型过滤（假设contentTypes参数存在）
        if (contentTypes != null && !contentTypes.isEmpty()) {
            boolQueryBuilder.filter(fq -> fq
                    .terms(t -> t
                            .field("contentTypes")
                            .terms(b -> b
                                    .value(contentTypes.stream()
                                            .map(FieldValue::of)
                                            .collect(Collectors.toList())
                                    )
                            )
                    )
            );
        }

        // 信息属性过滤
        if (sensitivityType != null) {
            boolQueryBuilder.filter(fq -> fq
                    .terms(t -> t
                            .field("sensitivityTypes")
                            .terms(b -> b
                                    .value(sensitivityType.stream()
                                            .map(FieldValue::of)  // 将Integer转换为FieldValue类型
                                            .collect(Collectors.toList())))
                    )
            );
        }

        // 处理内容类型搜索
        if (isOriginal != null) {
            boolQueryBuilder.filter(fq -> fq
                    .term(t -> t
                            .field("isOriginal")
                            .value(isOriginal)));
        }

        // 处理行业信息搜索
        if (secondTrades != null && !secondTrades.isEmpty()) {
            boolQueryBuilder.filter(fq -> fq
                    .terms(t -> t
                            .field("secondTrade.keyword")
                            .terms(b -> b
                                    .value(secondTrades.stream()
                                            .map(FieldValue::of)
                                            .collect(Collectors
                                                    .toList())))));
        }

        // 处理信源级别搜索
        if (mediaLevel != null && !mediaLevel.isEmpty()) {
            boolQueryBuilder.filter(fq -> fq
                    .terms(t -> t
                            .field("mediaLevels")
                            .terms(b -> b
                                    .value(mediaLevel.stream()
                                            .map(FieldValue::of)
                                            .collect(Collectors
                                                    .toList())))));
        }

        // 时间范围过滤
        if (startTime != null && endTime != null) {
            boolQueryBuilder.filter(fq -> fq
                    .range(r -> r
                            .field("publishTime")
                            .gte(JsonData.of(startTime))
                            .lte(JsonData.of(endTime))
                    )
            );
        }

        return boolQueryBuilder;
    }
} 