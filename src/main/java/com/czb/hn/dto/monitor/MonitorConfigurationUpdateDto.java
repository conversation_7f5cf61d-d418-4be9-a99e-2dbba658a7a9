package com.czb.hn.dto.monitor;

import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;

import javax.annotation.Nullable;
import java.util.List;

public record MonitorConfigurationUpdateDto(

        @Schema(description = "Associated plan ID", example = "1")
        Long planId,

        @Schema(description = "Monitor time", example = "2", allowableValues = {"0", "1", "2", "3"})
        Integer time,

        @Schema(description = "Start time", example = "2025-07-01 00:00:00")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Nullable
        String startTime,

        @Schema(description = "End time", example = "2025-07-01 23:59:59")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Nullable
        String endTime,

        @Schema(description = "Sort rule", example = "1", allowableValues = {"1", "2", "3", "4"})
        Integer sortRule,

        @Schema(description = "Information sensitivity type", example = "0", allowableValues = {"1", "2", "3"})
        @Nullable
        Integer sensitivityType,

        @Schema(description = "Similarity display rule", example = "false")
        Boolean SimilarityDisplayRule,

        @Schema(description = "Match method", example = "0", allowableValues = {"0", "1", "2"})
        Integer MatchMethod,

        @Schema(description = "Media type")
        @Nullable
        List<String> MediaType,

        @Schema(description = "Media type second")
        @Nullable
        List<String> MediaTypeSecond,

        @Schema(description = "Content type", examples = {"1","2"}, allowableValues = {"1", "2", "3", "4"})
        @Nullable
        List<Integer> ContentType,

        @Schema(description = "Is original", example = "1", allowableValues = {"1", "2"})
        @Nullable
        Integer isOriginal,

        @Schema(description = "Image text mode", example = "1", allowableValues = {"1", "2"})
        @Nullable
        Integer imageTextMode,

        @Schema(description = "Second trades")
        @Nullable
        List<String> SecondTrades,

        @Schema(description = "Author followers count min", example = "0")
        @Nullable
        Long authorFollowersCountMin,

        @Schema(description = "Author followers count max", example = "100000000")
        @Nullable
        Long authorFollowersCountMax,

        @Schema(description = "Media level")
        @Nullable
        List<String> MediaLevel
) {
        public MonitorConfigurationUpdateDto {
                if (planId == null) {
                        throw new IllegalArgumentException("Plan ID cannot be null.");
                }
                if (time == null) {
                        throw new IllegalArgumentException("Time cannot be null.");
                }
                if (sortRule == null) {
                        throw new IllegalArgumentException("Sort rule cannot be null.");
                }
                if (MatchMethod == null) {
                        throw new IllegalArgumentException("Match method cannot be null.");
                }
                validateEnumValueInt(time, List.of(0,1,2,3),
                        "time");
                validateEnumValueInt(sortRule, List.of(1,2,3,4),
                        "sortRule");
                validateEnumValueInt(sensitivityType, List.of(1,2,3),
                        "sensitivityTypes");
                validateEnumValueInt(MatchMethod, List.of(0,1,2),
                        "matchMethod");
                validateListEnumValuesInt(ContentType, List.of(1,2,3,4),
                        "ContentType");
                validateEnumValueInt(isOriginal, List.of(1,2),
                        "isOriginal");
                validateEnumValueInt(imageTextMode, List.of(1,2),
                        "imageTextMode");
                if (SimilarityDisplayRule == false && sortRule ==3) {
                        throw new IllegalArgumentException("Similarity display rule cannot be false when sort rule is 相似文章数.");
                }
                if (time == 0 && (startTime == null || endTime == null)) {
                        throw new IllegalArgumentException("Start time and end time cannot be null when time is 自定义.");
                }
        }

        private static void validateEnumValue(String value, List<String> allowedValues, String fieldName) {
                if (value != null && !allowedValues.contains(value)) {
                        throw new IllegalArgumentException(fieldName + " must be one of: " + allowedValues);
                }
        }
        private static void validateEnumValueInt(Integer value, List<Integer> allowedValues, String fieldName) {
                if (value != null && !allowedValues.contains(value)) {
                        throw new IllegalArgumentException(fieldName + " must be one of: " + allowedValues);
                }
        }

        private static void validateListEnumValues(List<String> values, List<String> allowedValues, String fieldName) {
                if (values != null) {
                        for (String value : values) {
                                if (value != null && !allowedValues.contains(value)) {
                                        throw new IllegalArgumentException(
                                                fieldName + " contains invalid value '" + value
                                                        + "'. Must be one of: " + allowedValues);
                                }
                        }
                }
        }
        private static void validateListEnumValuesInt(List<Integer> values, List<Integer> allowedValues, String fieldName) {
                if (values != null) {
                        for (Integer value : values) {
                                if (value != null && !allowedValues.contains(value)) {
                                        throw new IllegalArgumentException(
                                                fieldName + " contains invalid value '" + value
                                                        + "'. Must be one of: " + allowedValues);
                                }
                        }
                }
        }
}
