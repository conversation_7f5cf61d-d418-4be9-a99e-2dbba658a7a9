package com.czb.hn.dto.briefing.config;

import com.czb.hn.dto.alert.config.ReceptionSettingsDto;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

public record BriefingReceptionSettingsDto(
        @Schema(description = "Reception time", example = "10:00")
        String receptionTime,

        @Schema(description = "Reception methods configuration")
        ReceptionMethodsDto receptionMethods

) {
    public BriefingReceptionSettingsDto {
        if (receptionTime == null || receptionTime.isBlank()) {
            throw new IllegalArgumentException("Reception time cannot be null or blank");
        }
        if (!receptionTime.matches("^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$")) {
            throw new IllegalArgumentException("Invalid reception time format (must be HH:mm)");
        }
        if (receptionMethods == null) {
            throw new IllegalArgumentException("Reception methods cannot be null");
        }
    }

    /**
     * DTO for reception methods configuration
     */
    public record ReceptionMethodsDto(
            @Schema(description = "Email notification configuration")
            EmailConfigDto email,

            @Schema(description = "SMS notification configuration")
            SmsConfigDto sms
    ) {
        // No additional validation needed here
    }

    /**
     * DTO for email notification configuration
     */
    public record EmailConfigDto(
            @Schema(description = "Whether email notifications are enabled", example = "true")
            Boolean enabled,

            @Schema(description = "Email recipients list")
            List<EmailRecipientDto> recipients
    ) {
        public EmailConfigDto {
            if (enabled == null) {
                enabled = false;
            }
            if (enabled && (recipients == null || recipients.isEmpty())) {
                throw new IllegalArgumentException("Email recipients cannot be empty when email is enabled");
            }
            if (recipients != null && recipients.size() > 20) {
                throw new IllegalArgumentException("Cannot have more than 20 email recipients");
            }
        }
    }

    /**
     * DTO for SMS notification configuration
     */
    public record SmsConfigDto(
            @Schema(description = "Whether SMS notifications are enabled", example = "true")
            Boolean enabled,

            @Schema(description = "SMS recipients list")
            List<SmsRecipientDto> recipients
    ) {
        public SmsConfigDto {
            if (enabled == null) {
                enabled = false;
            }
            if (enabled && (recipients == null || recipients.isEmpty())) {
                throw new IllegalArgumentException("SMS recipients cannot be empty when SMS is enabled");
            }
            if (recipients != null && recipients.size() > 10) {
                throw new IllegalArgumentException("Cannot have more than 10 SMS recipients");
            }
        }
    }

    /**
     * DTO for email recipient
     */
    public record EmailRecipientDto(
            @Schema(description = "isActive", example = "true", requiredMode = Schema.RequiredMode.REQUIRED)
            Boolean isActive,

            @Schema(description = "Recipient name", example = "John Doe", requiredMode = Schema.RequiredMode.REQUIRED)
            String name,

            @Schema(description = "Email address", example = "<EMAIL>", requiredMode = Schema.RequiredMode.REQUIRED)
            String email
    ) {
        public EmailRecipientDto {
            if (name == null || name.isBlank()) {
                throw new IllegalArgumentException("Recipient name cannot be null or blank");
            }
            if (name.length() > 100) {
                throw new IllegalArgumentException("Recipient name cannot exceed 100 characters");
            }
            if (email == null || email.isBlank()) {
                throw new IllegalArgumentException("Email address cannot be null or blank");
            }
            if (!email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$")) {
                throw new IllegalArgumentException("Invalid email address format");
            }
        }
    }

    /**
     * DTO for SMS recipient
     */
    public record SmsRecipientDto(
            @Schema(description = "isActive", example = "true", requiredMode = Schema.RequiredMode.REQUIRED)
            Boolean isActive,

            @Schema(description = "Recipient name", example = "John Doe", requiredMode = Schema.RequiredMode.REQUIRED)
            String name,

            @Schema(description = "Phone number", example = "13800138000", requiredMode = Schema.RequiredMode.REQUIRED)
            String phone
    ) {
        public SmsRecipientDto {
            if (name == null || name.isBlank()) {
                throw new IllegalArgumentException("Recipient name cannot be null or blank");
            }
            if (name.length() > 100) {
                throw new IllegalArgumentException("Recipient name cannot exceed 100 characters");
            }
            if (phone == null || phone.isBlank()) {
                throw new IllegalArgumentException("Phone number cannot be null or blank");
            }
            if (!phone.matches("^1[3-9]\\d{9}$")) {
                throw new IllegalArgumentException("Invalid phone number format (must be Chinese mobile number)");
            }
        }
    }
}
